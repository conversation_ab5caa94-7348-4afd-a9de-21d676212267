"""
5层BEM模型构建器
用于从MRI数据构建包含头皮、颅骨、脑脊液、灰质、白质的5层BEM模型
"""

import os
import numpy as np
import nibabel as nib
from scipy import ndimage
from skimage import measure, morphology
import matplotlib.pyplot as plt
import mne
from mne.bem import make_bem_model, make_bem_solution
import warnings
warnings.filterwarnings('ignore')

class BEMModelBuilder:
    """5层BEM模型构建器"""
    
    def __init__(self, mri_file_path):
        """
        初始化BEM模型构建器
        
        Parameters:
        -----------
        mri_file_path : str
            MRI文件路径（.nii或.nii.gz格式）
        """
        self.mri_file_path = mri_file_path
        self.mri_data = None
        self.affine = None
        self.header = None
        self.segmentation = None
        self.surfaces = {}
        self.bem_model = None
        self.bem_solution = None
        
        # 5层组织的电导率值 (S/m)
        self.conductivities = {
            'scalp': 0.33,      # 头皮
            'skull': 0.0042,    # 颅骨
            'csf': 1.79,        # 脑脊液
            'gray_matter': 0.33, # 灰质
            'white_matter': 0.14 # 白质
        }
        
        print(f"初始化BEM模型构建器，MRI文件: {mri_file_path}")
    
    def load_mri_data(self):
        """加载MRI数据"""
        try:
            print("正在加载MRI数据...")
            nii_img = nib.load(self.mri_file_path)
            self.mri_data = nii_img.get_fdata()
            self.affine = nii_img.affine
            self.header = nii_img.header
            
            print(f"MRI数据形状: {self.mri_data.shape}")
            print(f"体素尺寸: {self.header.get_zooms()}")
            print("MRI数据加载完成")
            return True
            
        except Exception as e:
            print(f"加载MRI数据失败: {e}")
            return False
    
    def preprocess_mri(self):
        """预处理MRI数据"""
        print("正在预处理MRI数据...")
        
        # 标准化强度值
        self.mri_data = (self.mri_data - np.min(self.mri_data)) / (np.max(self.mri_data) - np.min(self.mri_data))
        
        # 高斯滤波去噪
        self.mri_data = ndimage.gaussian_filter(self.mri_data, sigma=0.5)
        
        print("MRI数据预处理完成")
    
    def segment_tissues(self):
        """组织分割 - 简化版本，实际应用中建议使用FreeSurfer"""
        print("正在进行组织分割...")
        
        # 创建分割掩码
        self.segmentation = np.zeros_like(self.mri_data, dtype=np.uint8)
        
        # 基于强度阈值的简单分割
        # 这是一个简化版本，实际应用中应使用更复杂的分割算法
        
        # 背景
        background_mask = self.mri_data < 0.1
        
        # 头皮 (最外层，高强度)
        scalp_mask = (self.mri_data > 0.3) & (self.mri_data < 0.7)
        scalp_mask = self._get_outer_surface_mask(scalp_mask)
        
        # 颅骨 (中等强度)
        skull_mask = (self.mri_data > 0.2) & (self.mri_data < 0.5)
        skull_mask = skull_mask & ~scalp_mask
        
        # 脑组织 (内部高强度)
        brain_mask = self.mri_data > 0.4
        brain_mask = self._get_brain_mask(brain_mask)
        
        # 脑脊液 (低强度，在脑内)
        csf_mask = (self.mri_data > 0.1) & (self.mri_data < 0.3) & brain_mask
        
        # 灰质和白质分割
        gray_matter_mask, white_matter_mask = self._segment_gray_white_matter(brain_mask)
        
        # 分配标签
        self.segmentation[scalp_mask] = 1      # 头皮
        self.segmentation[skull_mask] = 2      # 颅骨
        self.segmentation[csf_mask] = 3        # 脑脊液
        self.segmentation[gray_matter_mask] = 4 # 灰质
        self.segmentation[white_matter_mask] = 5 # 白质
        
        print("组织分割完成")
        print(f"头皮体素数: {np.sum(scalp_mask)}")
        print(f"颅骨体素数: {np.sum(skull_mask)}")
        print(f"脑脊液体素数: {np.sum(csf_mask)}")
        print(f"灰质体素数: {np.sum(gray_matter_mask)}")
        print(f"白质体素数: {np.sum(white_matter_mask)}")
    
    def _get_outer_surface_mask(self, mask):
        """获取外表面掩码"""
        # 形态学操作获取外表面
        eroded = morphology.binary_erosion(mask, morphology.ball(2))
        return mask & ~eroded
    
    def _get_brain_mask(self, high_intensity_mask):
        """获取脑掩码"""
        # 移除小的连通区域
        labeled = measure.label(high_intensity_mask)
        props = measure.regionprops(labeled)
        
        if props:
            # 选择最大的连通区域作为脑区域
            largest_region = max(props, key=lambda x: x.area)
            brain_mask = labeled == largest_region.label
            
            # 填充孔洞
            brain_mask = ndimage.binary_fill_holes(brain_mask)
            return brain_mask
        
        return high_intensity_mask
    
    def _segment_gray_white_matter(self, brain_mask):
        """分割灰质和白质"""
        brain_data = self.mri_data * brain_mask
        
        # 使用Otsu阈值分割灰质和白质
        from skimage.filters import threshold_otsu
        
        brain_values = brain_data[brain_mask]
        if len(brain_values) > 0:
            threshold = threshold_otsu(brain_values)
            
            white_matter_mask = (brain_data > threshold) & brain_mask
            gray_matter_mask = (brain_data <= threshold) & brain_mask & (brain_data > 0.1)
            
            return gray_matter_mask, white_matter_mask
        
        return np.zeros_like(brain_mask), np.zeros_like(brain_mask)

    def extract_surfaces(self):
        """从分割结果提取5层表面"""
        print("正在提取表面...")

        tissue_labels = {
            'scalp': 1,
            'skull': 2,
            'csf': 3,
            'gray_matter': 4,
            'white_matter': 5
        }

        for tissue_name, label in tissue_labels.items():
            print(f"提取{tissue_name}表面...")

            # 创建二值掩码
            mask = self.segmentation == label

            if np.sum(mask) == 0:
                print(f"警告: {tissue_name}区域为空")
                continue

            # 使用marching cubes算法提取表面
            try:
                verts, faces, normals, values = measure.marching_cubes(
                    mask.astype(float),
                    level=0.5,
                    spacing=self.header.get_zooms()
                )

                # 转换到世界坐标系
                verts_world = self._voxel_to_world(verts)

                # 简化网格
                verts_simplified, faces_simplified = self._simplify_mesh(verts_world, faces)

                self.surfaces[tissue_name] = {
                    'vertices': verts_simplified,
                    'faces': faces_simplified,
                    'normals': normals
                }

                print(f"{tissue_name}表面: {len(verts_simplified)}个顶点, {len(faces_simplified)}个面")

            except Exception as e:
                print(f"提取{tissue_name}表面失败: {e}")

        print("表面提取完成")

    def _voxel_to_world(self, verts):
        """将体素坐标转换为世界坐标"""
        # 添加齐次坐标
        verts_homo = np.column_stack([verts, np.ones(len(verts))])
        # 应用仿射变换
        verts_world = np.dot(verts_homo, self.affine.T)[:, :3]
        return verts_world

    def _simplify_mesh(self, vertices, faces, target_reduction=0.5):
        """简化网格以减少计算复杂度"""
        try:
            import trimesh

            # 创建trimesh对象
            mesh = trimesh.Trimesh(vertices=vertices, faces=faces)

            # 简化网格
            target_faces = int(len(faces) * target_reduction)
            simplified = mesh.simplify_quadric_decimation(target_faces)

            return simplified.vertices, simplified.faces

        except ImportError:
            print("警告: trimesh未安装，跳过网格简化")
            return vertices, faces
        except Exception as e:
            print(f"网格简化失败: {e}")
            return vertices, faces

    def build_bem_model(self):
        """构建BEM模型"""
        print("正在构建BEM模型...")

        if not self.surfaces:
            print("错误: 没有可用的表面数据")
            return False

        try:
            # 准备BEM表面数据
            bem_surfaces = []

            # 按照从外到内的顺序排列表面
            surface_order = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
            conductivity_values = []

            for surface_name in surface_order:
                if surface_name in self.surfaces:
                    surface_data = self.surfaces[surface_name]

                    # 创建MNE表面格式
                    bem_surface = {
                        'rr': surface_data['vertices'] / 1000.0,  # 转换为米
                        'tris': surface_data['faces'],
                        'ntri': len(surface_data['faces']),
                        'np': len(surface_data['vertices']),
                        'coord_frame': 0  # MRI坐标系
                    }

                    bem_surfaces.append(bem_surface)
                    conductivity_values.append(self.conductivities[surface_name])

                    print(f"添加{surface_name}表面到BEM模型")

            if len(bem_surfaces) == 0:
                print("错误: 没有有效的表面用于BEM模型")
                return False

            # 创建BEM模型
            self.bem_model = bem_surfaces

            # 计算BEM解
            print("正在计算BEM解...")
            self.bem_solution = self._compute_bem_solution(conductivity_values)

            print("BEM模型构建完成")
            return True

        except Exception as e:
            print(f"构建BEM模型失败: {e}")
            return False

    def _compute_bem_solution(self, conductivities):
        """计算BEM解"""
        try:
            # 这里应该实现实际的BEM求解
            # 由于复杂性，这里提供一个简化版本

            print(f"使用电导率值: {conductivities}")

            # 创建简化的BEM解结构
            bem_solution = {
                'surfaces': self.bem_model,
                'conductivities': conductivities,
                'method': 'simplified_bem',
                'is_sphere': False
            }

            return bem_solution

        except Exception as e:
            print(f"计算BEM解失败: {e}")
            return None

    def visualize_model(self, save_path=None):
        """可视化BEM模型"""
        try:
            import pyvista as pv

            print("正在创建3D可视化...")

            # 创建绘图器
            plotter = pv.Plotter()

            # 颜色映射
            colors = {
                'scalp': 'pink',
                'skull': 'white',
                'csf': 'lightblue',
                'gray_matter': 'gray',
                'white_matter': 'lightgray'
            }

            # 透明度设置
            alphas = {
                'scalp': 0.3,
                'skull': 0.4,
                'csf': 0.5,
                'gray_matter': 0.6,
                'white_matter': 0.8
            }

            # 添加每个表面
            for surface_name, surface_data in self.surfaces.items():
                vertices = surface_data['vertices']
                faces = surface_data['faces']

                # 创建PyVista网格
                mesh = pv.PolyData(vertices, np.column_stack([
                    np.full(len(faces), 3), faces
                ]).flatten())

                # 添加到绘图器
                plotter.add_mesh(
                    mesh,
                    color=colors.get(surface_name, 'red'),
                    opacity=alphas.get(surface_name, 0.5),
                    label=surface_name
                )

            # 设置相机和显示
            plotter.add_legend()
            plotter.show_axes()
            plotter.set_background('black')

            if save_path:
                plotter.screenshot(save_path)
                print(f"可视化结果已保存到: {save_path}")

            plotter.show()

        except ImportError:
            print("警告: pyvista未安装，无法进行3D可视化")
            self._plot_2d_slices()
        except Exception as e:
            print(f"可视化失败: {e}")

    def _plot_2d_slices(self):
        """绘制2D切片图"""
        if self.segmentation is None:
            print("没有分割数据可显示")
            return

        fig, axes = plt.subplots(2, 3, figsize=(15, 10))

        # 获取中心切片
        center_x = self.segmentation.shape[0] // 2
        center_y = self.segmentation.shape[1] // 2
        center_z = self.segmentation.shape[2] // 2

        # 矢状面
        axes[0, 0].imshow(self.mri_data[center_x, :, :], cmap='gray')
        axes[0, 0].set_title('原始MRI - 矢状面')
        axes[1, 0].imshow(self.segmentation[center_x, :, :], cmap='tab10')
        axes[1, 0].set_title('分割结果 - 矢状面')

        # 冠状面
        axes[0, 1].imshow(self.mri_data[:, center_y, :], cmap='gray')
        axes[0, 1].set_title('原始MRI - 冠状面')
        axes[1, 1].imshow(self.segmentation[:, center_y, :], cmap='tab10')
        axes[1, 1].set_title('分割结果 - 冠状面')

        # 轴状面
        axes[0, 2].imshow(self.mri_data[:, :, center_z], cmap='gray')
        axes[0, 2].set_title('原始MRI - 轴状面')
        axes[1, 2].imshow(self.segmentation[:, :, center_z], cmap='tab10')
        axes[1, 2].set_title('分割结果 - 轴状面')

        plt.tight_layout()
        plt.show()

    def save_model(self, output_dir):
        """保存BEM模型"""
        os.makedirs(output_dir, exist_ok=True)

        try:
            # 保存表面数据
            for surface_name, surface_data in self.surfaces.items():
                surface_file = os.path.join(output_dir, f"{surface_name}_surface.npz")
                np.savez(surface_file,
                        vertices=surface_data['vertices'],
                        faces=surface_data['faces'])
                print(f"保存{surface_name}表面到: {surface_file}")

            # 保存BEM解
            if self.bem_solution:
                bem_file = os.path.join(output_dir, "bem_solution.npz")
                np.savez(bem_file,
                        conductivities=self.bem_solution['conductivities'],
                        method=self.bem_solution['method'])
                print(f"保存BEM解到: {bem_file}")

            # 保存分割结果
            if self.segmentation is not None:
                seg_img = nib.Nifti1Image(self.segmentation, self.affine, self.header)
                seg_file = os.path.join(output_dir, "segmentation.nii.gz")
                nib.save(seg_img, seg_file)
                print(f"保存分割结果到: {seg_file}")

            print(f"模型保存完成，输出目录: {output_dir}")

        except Exception as e:
            print(f"保存模型失败: {e}")

    def get_model_info(self):
        """获取模型信息"""
        info = {
            'mri_shape': self.mri_data.shape if self.mri_data is not None else None,
            'voxel_size': self.header.get_zooms() if self.header else None,
            'surfaces': {},
            'conductivities': self.conductivities,
            'bem_model_ready': self.bem_model is not None,
            'bem_solution_ready': self.bem_solution is not None
        }

        for surface_name, surface_data in self.surfaces.items():
            info['surfaces'][surface_name] = {
                'vertices_count': len(surface_data['vertices']),
                'faces_count': len(surface_data['faces'])
            }

        return info
