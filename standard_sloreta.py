"""
标准sLORETA源定位系统
基于Pascual-Marqui 2002年规范实现的标准化低分辨率脑电磁断层成像
支持中文显示和全通道EEG源定位
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager
import seaborn as sns
import nibabel as nib
from scipy import linalg
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']

class StandardSLORETA:
    """
    标准sLORETA源定位器
    基于Pascual-Mar<PERSON> (2002) 的规范实现
    """
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """
        初始化标准sLORETA系统
        
        Parameters:
        -----------
        bem_output_dir : str
            BEM模型输出目录
        eeg_file : str
            EEG信号文件路径
        """
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 数据存储
        self.eeg_data = None
        self.eeg_info = None
        self.leadfield_matrix = None
        self.source_positions = None
        self.noise_covariance = None
        self.inverse_operator = None
        self.source_estimates = None
        
        # sLORETA参数
        self.regularization_param = 0.05  # 正则化参数 λ
        self.n_sources = 5000  # 源点数量
        
        # 标准10-20电极位置 (MNI坐标系，单位：mm)
        self.electrode_positions = self._get_standard_electrode_positions_mni()
        
        print("🧠 标准sLORETA源定位系统初始化完成")
        print(f"📁 BEM模型目录: {bem_output_dir}")
        print(f"📊 EEG信号文件: {eeg_file}")
        print(f"⚙️  正则化参数: {self.regularization_param}")
    
    def _get_standard_electrode_positions_mni(self):
        """获取标准10-20电极位置 (MNI坐标系)"""
        # 基于国际10-20系统的标准电极位置 (MNI坐标，单位：mm)
        positions = {
            'Fp1': [-27, 70, -2],    'Fp2': [27, 70, -2],
            'F7':  [-54, 28, -8],    'F3':  [-39, 41, 32],
            'Fz':  [0, 41, 41],      'F4':  [39, 41, 32],
            'F8':  [54, 28, -8],     'T7':  [-64, -2, -6],
            'C3':  [-45, -2, 45],    'Cz':  [0, -2, 64],
            'C4':  [45, -2, 45],     'T8':  [64, -2, -6],
            'P7':  [-54, -52, 6],    'P3':  [-39, -66, 35],
            'Pz':  [0, -66, 41],     'P4':  [39, -66, 35],
            'P8':  [54, -52, 6],     'O1':  [-27, -88, 4],
            'O2':  [27, -88, 4]
        }
        return positions
    
    def load_eeg_data(self):
        """加载EEG数据"""
        print("\n📥 正在加载EEG数据...")
        
        try:
            # 读取压缩的CSV文件
            df = pd.read_csv(self.eeg_file, compression='gzip')
            
            print(f"   📊 原始数据形状: {df.shape}")
            
            # 提取EEG通道数据 (前19列)
            n_channels = 19
            eeg_signals = df.iloc[:, :n_channels].values.T  # 转置为 (n_channels, n_times)
            
            # 创建通道信息
            ch_names = list(self.electrode_positions.keys())[:n_channels]
            sfreq = 250.0  # 采样率 250Hz
            
            # 存储数据
            self.eeg_data = eeg_signals
            self.eeg_info = {
                'ch_names': ch_names,
                'sfreq': sfreq,
                'n_channels': n_channels,
                'n_times': eeg_signals.shape[1]
            }
            
            print(f"   ✅ EEG数据加载成功:")
            print(f"      🔢 通道数: {n_channels}")
            print(f"      ⏱️  采样点数: {eeg_signals.shape[1]:,}")
            print(f"      🎵 采样率: {sfreq} Hz")
            print(f"      ⏰ 时长: {eeg_signals.shape[1]/sfreq:.1f} 秒")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 加载EEG数据失败: {e}")
            return False
    
    def create_source_space(self):
        """创建源空间"""
        print("\n🎯 正在创建源空间...")
        
        try:
            # 从BEM分割结果创建源空间
            seg_file = f"{self.bem_output_dir}/segmentation.nii.gz"
            seg_img = nib.load(seg_file)
            seg_data = seg_img.get_fdata()
            
            # 获取脑组织区域 (灰质=4, 白质=5)
            brain_mask = (seg_data == 4) | (seg_data == 5)
            
            # 获取脑组织的体素坐标
            brain_coords = np.array(np.where(brain_mask)).T
            
            # 转换到MNI物理坐标 (mm)
            affine = seg_img.affine
            brain_coords_mni = nib.affines.apply_affine(affine, brain_coords)
            
            # 均匀采样源点
            n_brain_voxels = len(brain_coords_mni)
            if n_brain_voxels > self.n_sources:
                # 使用随机采样进行源点选择
                np.random.seed(42)
                indices = np.random.choice(n_brain_voxels, self.n_sources, replace=False)
                source_positions = brain_coords_mni[indices]
            else:
                source_positions = brain_coords_mni
            
            self.source_positions = source_positions
            
            print(f"   ✅ 源空间创建完成:")
            print(f"      🎯 源点数量: {len(source_positions):,}")
            print(f"      📍 坐标系: MNI (mm)")
            print(f"      🧠 覆盖区域: 灰质 + 白质")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 创建源空间失败: {e}")
            return False
    
    def compute_leadfield_matrix(self):
        """计算导联场矩阵"""
        print("\n⚡ 正在计算导联场矩阵...")
        
        try:
            n_channels = self.eeg_info['n_channels']
            n_sources = len(self.source_positions)
            
            # 获取电极位置 (MNI坐标，mm)
            electrode_pos = np.array([self.electrode_positions[ch] 
                                    for ch in self.eeg_info['ch_names']])
            
            # 计算导联场矩阵 L (n_channels × n_sources×3)
            # 使用改进的球形头模型
            leadfield = np.zeros((n_channels, n_sources * 3))
            
            print(f"   🔄 计算进度: ", end="")
            
            for i, elec_pos in enumerate(electrode_pos):
                if i % 5 == 0:
                    print(f"{i+1}/{n_channels} ", end="", flush=True)
                
                for j, src_pos in enumerate(self.source_positions):
                    # 计算电极到源点的向量
                    r_vec = elec_pos - src_pos  # mm
                    r_dist = np.linalg.norm(r_vec)  # mm
                    
                    if r_dist > 1.0:  # 避免奇点
                        # 标准化距离向量
                        r_unit = r_vec / r_dist
                        
                        # 球形头模型的导联场 (简化版本)
                        # 实际应用中应使用BEM求解
                        conductivity = 0.33  # S/m，平均脑组织电导率
                        
                        # 偶极子在三个方向的贡献
                        for k in range(3):
                            # 单位偶极子在第k个方向的电位
                            dipole_dir = np.zeros(3)
                            dipole_dir[k] = 1.0
                            
                            # 计算电位 (简化的球形模型)
                            potential = (1.0 / (4 * np.pi * conductivity)) * \
                                       np.dot(dipole_dir, r_unit) / (r_dist**2)
                            
                            leadfield[i, j*3 + k] = potential
            
            print("\n   ✅ 导联场矩阵计算完成:")
            print(f"      📐 矩阵形状: {leadfield.shape}")
            print(f"      🔢 电极数: {n_channels}")
            print(f"      🎯 源点数: {n_sources}")
            print(f"      📊 总参数: {leadfield.size:,}")
            
            self.leadfield_matrix = leadfield
            return True
            
        except Exception as e:
            print(f"   ❌ 计算导联场矩阵失败: {e}")
            return False

    def estimate_noise_covariance(self):
        """估计噪声协方差矩阵"""
        print("\n🔊 正在估计噪声协方差矩阵...")

        try:
            # 使用数据的前10%作为噪声估计
            n_times = self.eeg_data.shape[1]
            noise_samples = int(0.1 * n_times)
            noise_data = self.eeg_data[:, :noise_samples]

            # 计算经验协方差矩阵
            noise_cov = np.cov(noise_data)

            # 正则化协方差矩阵 (避免奇异)
            reg_param = 0.01 * np.trace(noise_cov) / noise_cov.shape[0]
            noise_cov_reg = noise_cov + reg_param * np.eye(noise_cov.shape[0])

            self.noise_covariance = noise_cov_reg

            print(f"   ✅ 噪声协方差估计完成:")
            print(f"      📊 协方差矩阵形状: {noise_cov.shape}")
            print(f"      🔧 正则化参数: {reg_param:.6f}")
            print(f"      📈 条件数: {np.linalg.cond(noise_cov_reg):.2e}")

            return True

        except Exception as e:
            print(f"   ❌ 估计噪声协方差失败: {e}")
            return False

    def compute_sloreta_inverse_operator(self):
        """
        计算标准sLORETA逆算子
        基于Pascual-Marqui (2002) 的规范实现
        """
        print("\n🧮 正在计算标准sLORETA逆算子...")

        try:
            L = self.leadfield_matrix  # 导联场矩阵 (n_channels × n_sources×3)
            C = self.noise_covariance  # 噪声协方差矩阵 (n_channels × n_channels)
            lambda_reg = self.regularization_param  # 正则化参数

            print(f"   📐 导联场矩阵: {L.shape}")
            print(f"   📊 噪声协方差: {C.shape}")
            print(f"   ⚙️  正则化参数: {lambda_reg}")

            # 步骤1: 计算C的逆矩阵
            print("   🔄 步骤1: 计算噪声协方差逆矩阵...")
            C_inv = linalg.inv(C)

            # 步骤2: 计算 L^T * C^(-1) * L
            print("   🔄 步骤2: 计算 L^T * C^(-1) * L...")
            LT_Cinv = L.T @ C_inv
            LT_Cinv_L = LT_Cinv @ L

            # 步骤3: 添加正则化项
            print("   🔄 步骤3: 添加正则化项...")
            n_sources_total = L.shape[1]  # n_sources × 3
            reg_matrix = lambda_reg * np.trace(LT_Cinv_L) / n_sources_total * np.eye(n_sources_total)

            # 步骤4: 计算MNE逆算子 W_MNE
            print("   🔄 步骤4: 计算MNE逆算子...")
            W_MNE = linalg.inv(LT_Cinv_L + reg_matrix) @ LT_Cinv

            # 步骤5: 计算sLORETA标准化矩阵 S
            print("   🔄 步骤5: 计算sLORETA标准化矩阵...")
            # S = W_MNE * L (分辨率矩阵)
            S = W_MNE @ L

            # 步骤6: 计算sLORETA逆算子
            print("   🔄 步骤6: 计算sLORETA逆算子...")
            W_sLORETA = np.zeros_like(W_MNE)

            # 对每个源点进行标准化
            n_sources = n_sources_total // 3
            for i in range(n_sources):
                # 获取第i个源点的3×3分辨率子矩阵
                idx_start = i * 3
                idx_end = (i + 1) * 3

                S_ii = S[idx_start:idx_end, idx_start:idx_end]  # 3×3矩阵

                # 计算标准化因子 (S_ii的迹的平方根)
                trace_S_ii = np.trace(S_ii)

                if trace_S_ii > 1e-12:  # 避免除零
                    std_factor = 1.0 / np.sqrt(trace_S_ii)
                    W_sLORETA[idx_start:idx_end, :] = std_factor * W_MNE[idx_start:idx_end, :]
                else:
                    W_sLORETA[idx_start:idx_end, :] = W_MNE[idx_start:idx_end, :]

            self.inverse_operator = {
                'W_sLORETA': W_sLORETA,
                'W_MNE': W_MNE,
                'S': S,
                'regularization': lambda_reg,
                'method': 'sLORETA'
            }

            print(f"   ✅ sLORETA逆算子计算完成:")
            print(f"      📐 逆算子形状: {W_sLORETA.shape}")
            print(f"      🎯 源点数: {n_sources}")
            print(f"      📊 条件数: {np.linalg.cond(LT_Cinv_L + reg_matrix):.2e}")

            return True

        except Exception as e:
            print(f"   ❌ 计算sLORETA逆算子失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def apply_sloreta_inverse(self):
        """应用sLORETA逆算子进行源定位"""
        print("\n🎯 正在应用sLORETA逆算子...")

        try:
            W_sLORETA = self.inverse_operator['W_sLORETA']
            eeg_data = self.eeg_data

            print(f"   📊 EEG数据形状: {eeg_data.shape}")
            print(f"   🧮 逆算子形状: {W_sLORETA.shape}")

            # 应用逆算子: J = W * M
            print("   🔄 正在计算源活动...")
            source_activity = W_sLORETA @ eeg_data

            # 计算源强度 (每个源点3个方向的模长)
            n_sources = source_activity.shape[0] // 3
            n_times = source_activity.shape[1]

            source_strength = np.zeros((n_sources, n_times))

            print("   🔄 正在计算源强度...")
            for i in range(n_sources):
                # 获取第i个源点的3个方向分量
                x = source_activity[i*3, :]
                y = source_activity[i*3+1, :]
                z = source_activity[i*3+2, :]

                # 计算向量模长
                source_strength[i, :] = np.sqrt(x**2 + y**2 + z**2)

            # 存储结果
            self.source_estimates = {
                'source_activity': source_activity,  # 原始源活动 (3×n_sources, n_times)
                'source_strength': source_strength,  # 源强度 (n_sources, n_times)
                'source_positions': self.source_positions,  # 源位置 (n_sources, 3)
                'method': 'sLORETA',
                'regularization': self.regularization_param,
                'n_sources': n_sources,
                'n_times': n_times
            }

            print(f"   ✅ sLORETA源定位完成:")
            print(f"      🎯 源点数: {n_sources:,}")
            print(f"      ⏱️  时间点数: {n_times:,}")
            print(f"      📈 最大源强度: {np.max(source_strength):.6f}")
            print(f"      📊 平均源强度: {np.mean(source_strength):.6f}")

            return True

        except Exception as e:
            print(f"   ❌ 应用sLORETA逆算子失败: {e}")
            return False

    def run_complete_sloreta_analysis(self):
        """运行完整的sLORETA分析流程"""
        print("="*80)
        print("🧠 标准sLORETA源定位分析")
        print("   基于Pascual-Marqui (2002) 规范实现")
        print("="*80)

        # 执行完整流程
        steps = [
            ("加载EEG数据", self.load_eeg_data),
            ("创建源空间", self.create_source_space),
            ("计算导联场矩阵", self.compute_leadfield_matrix),
            ("估计噪声协方差", self.estimate_noise_covariance),
            ("计算sLORETA逆算子", self.compute_sloreta_inverse_operator),
            ("应用sLORETA逆算子", self.apply_sloreta_inverse)
        ]

        for step_name, step_func in steps:
            print(f"\n{'='*60}")
            print(f"🔄 {step_name}")
            print(f"{'='*60}")

            if not step_func():
                print(f"❌ {step_name}失败，分析终止")
                return False

        print(f"\n{'='*80}")
        print("🎉 标准sLORETA分析完成!")
        print(f"{'='*80}")

        return True

    def analyze_results(self):
        """分析sLORETA结果"""
        if self.source_estimates is None:
            print("❌ 没有源定位结果可分析")
            return None

        print("\n📊 sLORETA结果分析")
        print("="*50)

        source_strength = self.source_estimates['source_strength']
        source_positions = self.source_estimates['source_positions']

        # 基本统计
        max_strength = np.max(source_strength)
        mean_strength = np.mean(source_strength)
        std_strength = np.std(source_strength)

        # 找到最强源
        max_idx = np.unravel_index(np.argmax(source_strength), source_strength.shape)
        max_source_idx = max_idx[0]
        max_time_idx = max_idx[1]
        max_position = source_positions[max_source_idx]

        # 时间分析
        time_course = source_strength[max_source_idx, :]
        peak_time = max_time_idx / self.eeg_info['sfreq']

        # 空间分析
        spatial_extent = np.std(source_positions, axis=0)

        analysis_results = {
            'max_strength': max_strength,
            'mean_strength': mean_strength,
            'std_strength': std_strength,
            'max_position_mni': max_position,
            'max_time_s': peak_time,
            'max_time_idx': max_time_idx,
            'spatial_extent': spatial_extent,
            'n_sources': len(source_positions),
            'n_timepoints': source_strength.shape[1]
        }

        print(f"📈 最大源强度: {max_strength:.6f}")
        print(f"📊 平均源强度: {mean_strength:.6f} ± {std_strength:.6f}")
        print(f"📍 最强源位置 (MNI): ({max_position[0]:.1f}, {max_position[1]:.1f}, {max_position[2]:.1f}) mm")
        print(f"⏰ 最强激活时间: {peak_time:.3f} 秒 (第{max_time_idx}个采样点)")
        print(f"🗺️  空间分布范围: X±{spatial_extent[0]:.1f}, Y±{spatial_extent[1]:.1f}, Z±{spatial_extent[2]:.1f} mm")
        print(f"🎯 总源点数: {len(source_positions):,}")
        print(f"⏱️  总时间点数: {source_strength.shape[1]:,}")

        return analysis_results

    def create_comprehensive_visualization(self, save_dir='sloreta_results'):
        """创建全面的可视化结果"""
        import os
        os.makedirs(save_dir, exist_ok=True)

        if self.source_estimates is None:
            print("❌ 没有源定位结果可可视化")
            return

        print(f"\n🎨 创建sLORETA可视化结果...")

        source_strength = self.source_estimates['source_strength']
        source_positions = self.source_estimates['source_positions']

        # 创建综合图形
        fig = plt.figure(figsize=(20, 16))
        fig.suptitle('标准sLORETA源定位结果 (Pascual-Marqui 2002)',
                    fontsize=16, fontweight='bold')

        # 1. 源强度时间序列 (最强的10个源)
        ax1 = plt.subplot(3, 3, 1)
        top_sources = np.argsort(np.max(source_strength, axis=1))[-10:]
        time_axis = np.arange(source_strength.shape[1]) / self.eeg_info['sfreq']

        for i, src_idx in enumerate(top_sources):
            plt.plot(time_axis, source_strength[src_idx, :],
                    alpha=0.7, label=f'源点 {src_idx}')

        plt.xlabel('时间 (秒)')
        plt.ylabel('源强度')
        plt.title('最强10个源的时间序列')
        plt.grid(True, alpha=0.3)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 2. 全脑源强度分布 (最大激活时刻)
        max_time_idx = np.argmax(np.sum(source_strength, axis=0))
        current_strength = source_strength[:, max_time_idx]

        # XY平面投影
        ax2 = plt.subplot(3, 3, 2)
        scatter = plt.scatter(source_positions[:, 0], source_positions[:, 1],
                            c=current_strength, cmap='hot', s=2, alpha=0.6)
        plt.colorbar(scatter, label='源强度')
        plt.xlabel('X (mm, MNI)')
        plt.ylabel('Y (mm, MNI)')
        plt.title(f'源分布 - XY平面 (t={max_time_idx/self.eeg_info["sfreq"]:.3f}s)')
        plt.grid(True, alpha=0.3)

        # XZ平面投影
        ax3 = plt.subplot(3, 3, 3)
        scatter = plt.scatter(source_positions[:, 0], source_positions[:, 2],
                            c=current_strength, cmap='hot', s=2, alpha=0.6)
        plt.colorbar(scatter, label='源强度')
        plt.xlabel('X (mm, MNI)')
        plt.ylabel('Z (mm, MNI)')
        plt.title(f'源分布 - XZ平面 (t={max_time_idx/self.eeg_info["sfreq"]:.3f}s)')
        plt.grid(True, alpha=0.3)

        # YZ平面投影
        ax4 = plt.subplot(3, 3, 4)
        scatter = plt.scatter(source_positions[:, 1], source_positions[:, 2],
                            c=current_strength, cmap='hot', s=2, alpha=0.6)
        plt.colorbar(scatter, label='源强度')
        plt.xlabel('Y (mm, MNI)')
        plt.ylabel('Z (mm, MNI)')
        plt.title(f'源分布 - YZ平面 (t={max_time_idx/self.eeg_info["sfreq"]:.3f}s)')
        plt.grid(True, alpha=0.3)

        # 3. 源强度直方图
        ax5 = plt.subplot(3, 3, 5)
        plt.hist(current_strength, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('源强度')
        plt.ylabel('源点数量')
        plt.title('源强度分布直方图')
        plt.grid(True, alpha=0.3)

        # 4. 最强源的时频分析
        ax6 = plt.subplot(3, 3, 6)
        max_source_idx = np.argmax(np.max(source_strength, axis=1))
        max_source_signal = source_strength[max_source_idx, :]

        # 简单的功率谱密度
        from scipy.signal import welch
        freqs, psd = welch(max_source_signal, fs=self.eeg_info['sfreq'], nperseg=1024)
        plt.semilogy(freqs, psd)
        plt.xlabel('频率 (Hz)')
        plt.ylabel('功率谱密度')
        plt.title('最强源的功率谱')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 50)  # 只显示0-50Hz

        # 5. EEG通道数据 (原始信号)
        ax7 = plt.subplot(3, 3, 7)
        time_window_end = min(2500, self.eeg_data.shape[1])  # 前10秒
        time_axis_eeg = np.arange(time_window_end) / self.eeg_info['sfreq']

        for i, ch_name in enumerate(self.eeg_info['ch_names'][:5]):  # 显示前5个通道
            plt.plot(time_axis_eeg, self.eeg_data[i, :time_window_end] + i*50,
                    label=ch_name, alpha=0.8)

        plt.xlabel('时间 (秒)')
        plt.ylabel('电压 (μV)')
        plt.title('原始EEG信号 (前5通道)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 6. 源定位精度评估
        ax8 = plt.subplot(3, 3, 8)
        # 计算源强度的时间变异性
        temporal_var = np.var(source_strength, axis=1)
        spatial_var = np.var(source_strength, axis=0)

        plt.subplot(2, 1, 1)
        plt.plot(temporal_var)
        plt.xlabel('源点索引')
        plt.ylabel('时间变异性')
        plt.title('各源点的时间变异性')
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 1, 2)
        plt.plot(time_axis, spatial_var)
        plt.xlabel('时间 (秒)')
        plt.ylabel('空间变异性')
        plt.title('各时刻的空间变异性')
        plt.grid(True, alpha=0.3)

        # 7. 解剖区域分析
        ax9 = plt.subplot(3, 3, 9)
        # 根据MNI坐标划分脑区
        regions = self._classify_brain_regions(source_positions, current_strength)

        region_names = list(regions.keys())
        region_strengths = list(regions.values())

        bars = plt.bar(range(len(region_names)), region_strengths,
                      color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
        plt.xticks(range(len(region_names)), region_names, rotation=45)
        plt.ylabel('平均源强度')
        plt.title('各脑区的平均激活强度')
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, strength in zip(bars, region_strengths):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(region_strengths)*0.01,
                    f'{strength:.3f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图像
        save_path = f'{save_dir}/sloreta_comprehensive_analysis.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

        print(f"   ✅ 可视化结果已保存: {save_path}")

        return save_path

    def _classify_brain_regions(self, positions, strengths):
        """根据MNI坐标对脑区进行分类"""
        regions = {
            '前额叶': 0,
            '顶叶': 0,
            '颞叶': 0,
            '枕叶': 0,
            '中央区': 0
        }

        region_counts = {key: 0 for key in regions.keys()}

        for i, pos in enumerate(positions):
            x, y, z = pos
            strength = strengths[i]

            # 简化的脑区分类 (基于MNI坐标)
            if y > 20:  # 前部
                regions['前额叶'] += strength
                region_counts['前额叶'] += 1
            elif y < -40:  # 后部
                if z < 20:
                    regions['颞叶'] += strength
                    region_counts['颞叶'] += 1
                else:
                    regions['枕叶'] += strength
                    region_counts['枕叶'] += 1
            elif abs(x) < 30:  # 中央区
                regions['中央区'] += strength
                region_counts['中央区'] += 1
            else:  # 侧面
                regions['顶叶'] += strength
                region_counts['顶叶'] += 1

        # 计算平均强度
        for region in regions:
            if region_counts[region] > 0:
                regions[region] /= region_counts[region]

        return regions

    def save_results(self, save_dir='sloreta_results'):
        """保存sLORETA结果"""
        import os
        import json
        os.makedirs(save_dir, exist_ok=True)

        if self.source_estimates is None:
            print("❌ 没有结果可保存")
            return

        print(f"\n💾 保存sLORETA结果到: {save_dir}")

        # 保存源强度数据
        np.save(f"{save_dir}/sloreta_source_strength.npy",
                self.source_estimates['source_strength'])

        # 保存源位置
        np.save(f"{save_dir}/sloreta_source_positions.npy",
                self.source_estimates['source_positions'])

        # 保存原始源活动
        np.save(f"{save_dir}/sloreta_source_activity.npy",
                self.source_estimates['source_activity'])

        # 保存逆算子
        np.save(f"{save_dir}/sloreta_inverse_operator.npy",
                self.inverse_operator['W_sLORETA'])

        # 保存元数据
        metadata = {
            'method': 'sLORETA',
            'reference': 'Pascual-Marqui (2002)',
            'regularization_param': self.regularization_param,
            'n_sources': self.source_estimates['n_sources'],
            'n_timepoints': self.source_estimates['n_times'],
            'sampling_rate': self.eeg_info['sfreq'],
            'n_channels': self.eeg_info['n_channels'],
            'channel_names': self.eeg_info['ch_names'],
            'max_source_strength': float(np.max(self.source_estimates['source_strength'])),
            'mean_source_strength': float(np.mean(self.source_estimates['source_strength'])),
            'coordinate_system': 'MNI (mm)'
        }

        with open(f"{save_dir}/sloreta_metadata.json", 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        print(f"   ✅ 结果保存完成:")
        print(f"      📊 源强度: sloreta_source_strength.npy")
        print(f"      📍 源位置: sloreta_source_positions.npy")
        print(f"      ⚡ 源活动: sloreta_source_activity.npy")
        print(f"      🧮 逆算子: sloreta_inverse_operator.npy")
        print(f"      📋 元数据: sloreta_metadata.json")

def main():
    """主程序"""
    print("🧠 标准sLORETA源定位系统")
    print("   基于Pascual-Marqui (2002) 规范实现")
    print("   支持中文显示和全通道EEG源定位")
    print("="*80)

    # 检查必要文件
    required_files = ['signal-1.csv.gz', 'hdbet_bem_output']
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")

        if 'hdbet_bem_output' in missing_files:
            print("\n💡 请先运行BEM模型构建:")
            print("   python advanced_main.py sub-1_T1w.nii --method hd-bet")

        return

    try:
        # 创建sLORETA分析器
        sloreta = StandardSLORETA(
            bem_output_dir='hdbet_bem_output',
            eeg_file='signal-1.csv.gz'
        )

        # 运行完整分析
        if sloreta.run_complete_sloreta_analysis():

            # 分析结果
            print("\n" + "="*80)
            print("📊 结果分析")
            print("="*80)
            analysis_results = sloreta.analyze_results()

            # 创建可视化
            print("\n" + "="*80)
            print("🎨 创建可视化")
            print("="*80)
            sloreta.create_comprehensive_visualization()

            # 保存结果
            print("\n" + "="*80)
            print("💾 保存结果")
            print("="*80)
            sloreta.save_results()

            print("\n" + "="*80)
            print("🎉 标准sLORETA分析完成!")
            print("="*80)

            if analysis_results:
                print(f"\n🏆 关键结果:")
                print(f"   📈 最大源强度: {analysis_results['max_strength']:.6f}")
                print(f"   📍 最强源位置: ({analysis_results['max_position_mni'][0]:.1f}, "
                      f"{analysis_results['max_position_mni'][1]:.1f}, "
                      f"{analysis_results['max_position_mni'][2]:.1f}) mm (MNI)")
                print(f"   ⏰ 激活时间: {analysis_results['max_time_s']:.3f} 秒")
                print(f"   🎯 总源点数: {analysis_results['n_sources']:,}")

        else:
            print("❌ sLORETA分析失败")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import os
    main()
